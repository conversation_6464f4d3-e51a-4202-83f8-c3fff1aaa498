"""
Deltek API integration service for fetching invoice data
"""

import requests
import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from app.core.config import settings

logger = logging.getLogger(__name__)


class DeltekService:
    """Service class for Deltek API operations"""
    
    def __init__(self):
        """Initialize Deltek service with configuration"""
        self.base_url = settings.DELTEK_BASE_URL
        self.username = settings.DELTEK_USERNAME
        self.password = settings.DELTEK_PASSWORD
        self.client_id = settings.DELTEK_CLIENT_ID
        self.client_secret = settings.DELTEK_CLIENT_SECRET
        self.database = settings.DELTEK_DATABASE
        self.access_token = None
        self.token_expires_at = None
        
        # Common headers for all requests
        self.common_headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': 'AWSELB=939D1D61041420324A5921896E57F9660F7A5941A4E793375352D4DEEFE0E8BA7C145DB819781E7CABBC33EB93E83580086094C09625622784FF4567A39B7E2E54A1C389AA; AWSELBCORS=939D1D61041420324A5921896E57F9660F7A5941A4E793375352D4DEEFE0E8BA7C145DB819781E7CABBC33EB93E83580086094C09625622784FF4567A39B7E2E54A1C389AA'
        }
    
    def get_access_token(self) -> Dict[str, Any]:
        """
        Get access token from Deltek API
        
        Returns:
            Dict containing success status and token data or error message
        """
        try:
            logger.info("🔑 Requesting access token from Deltek API...")
            
            # Prepare token request data
            token_data = {
                'Username': self.username,
                'Password': self.password,
                'grant_type': 'password',
                'Integrated': 'N',
                'database': self.database,
                'Client_Id': self.client_id,
                'client_secret': self.client_secret
            }
            
            # Make token request
            response = requests.post(
                f"{self.base_url}/token",
                headers=self.common_headers,
                data=token_data,
                timeout=30
            )
            
            if response.status_code == 200:
                token_response = response.json()
                
                # Store token and expiration time
                self.access_token = token_response.get('access_token')
                expires_in = token_response.get('expires_in', 43199)  # Default to ~12 hours
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 300)  # Refresh 5 minutes early
                
                logger.info(f"✅ Successfully obtained Deltek access token (expires in {expires_in} seconds)")
                
                return {
                    "success": True,
                    "token_data": token_response,
                    "expires_at": self.token_expires_at
                }
            else:
                error_msg = f"Failed to get access token. Status: {response.status_code}, Response: {response.text}"
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "message": error_msg,
                    "status_code": response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error while getting access token: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg
            }
        except Exception as e:
            error_msg = f"Unexpected error while getting access token: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg
            }
    
    def is_token_valid(self) -> bool:
        """
        Check if current access token is valid and not expired
        
        Returns:
            True if token is valid, False otherwise
        """
        if not self.access_token or not self.token_expires_at:
            return False
        
        return datetime.now() < self.token_expires_at
    
    def ensure_valid_token(self) -> Dict[str, Any]:
        """
        Ensure we have a valid access token, refresh if needed
        
        Returns:
            Dict containing success status and token info or error message
        """
        if self.is_token_valid():
            logger.info("✅ Using existing valid Deltek access token")
            return {
                "success": True,
                "message": "Using existing valid token",
                "token": self.access_token
            }
        
        logger.info("🔄 Access token expired or missing, requesting new token...")
        return self.get_access_token()
    
    def get_ar_balance_invoices(self) -> Dict[str, Any]:
        """
        Fetch AR Balance invoices from Deltek API
        
        Returns:
            Dict containing success status and invoice data or error message
        """
        try:
            # Ensure we have a valid token
            token_result = self.ensure_valid_token()
            if not token_result["success"]:
                return token_result
            
            logger.info("📋 Fetching AR Balance invoices from Deltek API...")
            
            # Prepare headers with authorization
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': self.common_headers['Cookie']
            }
            
            # Make API request
            response = requests.get(
                f"{self.base_url}/ARReview/ARBalance",
                headers=headers,
                timeout=60
            )
            
            if response.status_code == 200:
                invoices = response.json()
                logger.info(f"✅ Successfully fetched {len(invoices)} invoices from Deltek API")
                
                return {
                    "success": True,
                    "invoices": invoices,
                    "count": len(invoices)
                }
            else:
                error_msg = f"Failed to fetch invoices. Status: {response.status_code}, Response: {response.text}"
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "message": error_msg,
                    "status_code": response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error while fetching invoices: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg
            }
        except Exception as e:
            error_msg = f"Unexpected error while fetching invoices: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg
            }
    
    def parse_invoice_data(self, invoice_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse Deltek invoice data into our application format
        
        Args:
            invoice_data: Raw invoice data from Deltek API
            
        Returns:
            Parsed invoice data ready for database insertion
        """
        try:
            # Extract and map fields according to requirements
            parsed_data = {
                "project_number": invoice_data.get("WBS1", ""),
                "client_name": invoice_data.get("BillingClient", ""),
                "invoice_id": invoice_data.get("Invoice", ""),
                "amount": float(invoice_data.get("Balance", 0.0)),
                "due_date": None,
                "project_name": invoice_data.get("WBS1Name", ""),
                "original_amount": float(invoice_data.get("OriginalAmt", 0.0)),
                "invoice_date": invoice_data.get("InvoiceDate"),
                "billing_client_id": invoice_data.get("BillingClientID", ""),
                "company": invoice_data.get("Company", ""),
                "credit_memo_ref_no": invoice_data.get("CreditMemoRefNo", "")
            }
            
            # Parse invoice date if available
            if parsed_data["invoice_date"]:
                try:
                    # Parse ISO format date: "2025-06-03T00:00:00.000"
                    parsed_data["invoice_date"] = datetime.fromisoformat(
                        parsed_data["invoice_date"].replace('T', ' ').replace('.000', '')
                    )
                except (ValueError, AttributeError):
                    parsed_data["invoice_date"] = None
            
            # Set due_date to invoice_date + 30 days if invoice_date is available
            if parsed_data["invoice_date"]:
                parsed_data["due_date"] = parsed_data["invoice_date"] + timedelta(days=30)
            
            return {
                "success": True,
                "parsed_data": parsed_data
            }
            
        except Exception as e:
            error_msg = f"Error parsing invoice data: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg,
                "raw_data": invoice_data
            }

    def create_cash_receipt(self, billing_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create cash receipt in Deltek after successful payment

        Args:
            billing_data: Dictionary containing billing and job information
                Required fields:
                - job_id: Job ID
                - job_name: Project name
                - deltek_project_number: Deltek WBS1
                - deltek_invoice_id: Deltek invoice ID
                - amount: Payment amount

        Returns:
            Dict containing success status and cash receipt data or error message
        """
        try:
            # Ensure we have a valid token
            token_result = self.ensure_valid_token()
            if not token_result["success"]:
                return token_result

            logger.info(f"💰 Creating cash receipt for job {billing_data.get('job_id')} in Deltek...")

            # Generate unique identifiers
            batch_id = f"INV-{billing_data.get('job_id', 'UNKNOWN')}"
            ref_no = f"REF-{billing_data.get('job_id', 'UNKNOWN')}"
            current_date = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3]  # Format: 2025-06-26T10:20:55.376

            # Prepare cash receipt data
            cash_receipt_data = {
                "Batch": batch_id,
                "RefNo": ref_no,
                "TransDate": current_date,
                "TransComment": billing_data.get("job_name", "Payment"),
                "Posted": "N",
                "BankCode": "101.80",
                "Seq": 1,
                "CurrencyExchangeOverrideMethod": "N",
                "CurrencyExchangeOverrideDate": "",
                "CurrencyExchangeOverrideRate": 0,
                "Status": "N",
                "AuthorizedBy": "",
                "RejectReason": "",
                "ModUser": "4A1E0A2E6B8748C5B855AAEEA7554AEA",
                "ModDate": current_date,
                "Diary": "",
                "crDetail": [
                    {
                        "Batch": batch_id,
                        "RefNo": ref_no,
                        "Seq": 0,
                        "Description": "",
                        "WBS1": billing_data.get("deltek_project_number", ""),
                        "WBS2": " ",
                        "WBS3": " ",
                        "Org": "STR_00A",
                        "Invoice": billing_data.get("deltek_invoice_id", ""),
                        "Account": "111.00",
                        "Amount": float(billing_data.get("amount", 0)),
                        "Interest": "N",
                        "TaxCode": "",
                        "TaxBasis": 0,
                        "Retainer": "N",
                        "CurrencyExchangeOverrideRate": 0,
                        "SourceAmount": float(billing_data.get("amount", 0)),
                        "SourceExchangeInfo": "",
                        "LinkCompany": "",
                        "PreInvoice": "",
                        "PKey": "a1a843f3176f4494ab250000761687ce",
                        "_originalValues": {},
                        "_transType": "I"
                    }
                ]
            }

            # Prepare headers with authorization
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.access_token}',
                'Cookie': self.common_headers['Cookie']
            }

            # Make API request to create cash receipt
            api_url = f"{self.base_url}/DataEntry/crMaster/{batch_id}|{ref_no}"

            logger.info(f"📡 Sending cash receipt to Deltek API: {api_url}")

            response = requests.post(
                api_url,
                headers=headers,
                json=cash_receipt_data,
                timeout=60
            )

            if response.status_code in [200, 201]:
                logger.info(f"✅ Successfully created cash receipt in Deltek for job {billing_data.get('job_id')}")

                return {
                    "success": True,
                    "cash_receipt_data": cash_receipt_data,
                    "batch_id": batch_id,
                    "ref_no": ref_no,
                    "deltek_response": response.json() if response.content else {}
                }
            else:
                error_msg = f"Failed to create cash receipt. Status: {response.status_code}, Response: {response.text}"
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "message": error_msg,
                    "status_code": response.status_code,
                    "cash_receipt_data": cash_receipt_data
                }

        except requests.exceptions.RequestException as e:
            error_msg = f"Network error while creating cash receipt: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg,
                "cash_receipt_data": cash_receipt_data if 'cash_receipt_data' in locals() else None
            }
        except Exception as e:
            error_msg = f"Unexpected error while creating cash receipt: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg,
                "cash_receipt_data": cash_receipt_data if 'cash_receipt_data' in locals() else None
            }


# Create global instance
deltek_service = DeltekService()
