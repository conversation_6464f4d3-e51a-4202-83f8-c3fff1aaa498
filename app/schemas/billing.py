from pydantic import BaseModel
from datetime import datetime, date
from typing import Optional
from decimal import Decimal

# Job details schema for billing responses
class BillingJobDetails(BaseModel):
    id: int
    job_name: str
    client_name: Optional[str] = None
    job_description: Optional[str] = None
    project_deadline: Optional[date] = None
    contract_amount: Optional[str] = None
    job_status: str
    service_type: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

class BillingBase(BaseModel):
    invoice_number: str
    customer_name: str
    invoice_amount: Decimal
    due_date: Optional[datetime] = None
    paid: bool = False
    # Stripe fields
    stripe_payment_intent_id: Optional[str] = None
    stripe_checkout_session_id: Optional[str] = None
    stripe_customer_id: Optional[str] = None
    payment_method_type: Optional[str] = None  # 'stripe_ach', 'stripe_card', 'stripe_all', etc.
    is_cash_receipt_generated: bool = False

class BillingCreate(BillingBase):
    user_id: int
    job_id: int
    invoice_date: Optional[datetime] = None

class BillingUpdate(BaseModel):
    customer_name: Optional[str] = None
    invoice_amount: Optional[Decimal] = None
    due_date: Optional[datetime] = None
    paid: Optional[bool] = None
    # Stripe fields
    stripe_payment_intent_id: Optional[str] = None
    stripe_checkout_session_id: Optional[str] = None
    stripe_customer_id: Optional[str] = None
    payment_method_type: Optional[str] = None
    is_cash_receipt_generated: Optional[bool] = None

class Billing(BillingBase):
    id: int
    user_id: int
    job_id: int
    invoice_date: datetime
    created_at: datetime
    updated_at: datetime
    job: Optional[BillingJobDetails] = None

    class Config:
        from_attributes = True
