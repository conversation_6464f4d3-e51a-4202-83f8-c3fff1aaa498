from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from decimal import Decimal
from app.models.job import JobStatus, ServiceType

# Document schema
class DocumentInfo(BaseModel):
    original_filename: str
    stored_filename: str
    file_path: str
    file_size: int
    content_type: Optional[str] = None
    uploaded_at: Optional[datetime] = None

# Billing schema for job response
class JobBilling(BaseModel):
    id: int
    invoice_number: str
    customer_name: str
    invoice_amount: Decimal
    invoice_date: datetime
    due_date: Optional[datetime] = None
    paid: bool = False
    created_at: datetime

    class Config:
        from_attributes = True

# Shared properties
class JobBase(BaseModel):
    job_name: str
    job_description: Optional[str] = None
    job_status: JobStatus = JobStatus.UNDER_REVIEW
    contract_term: Optional[str] = None
    contract_amount: Optional[str] = None
    payment_id: Optional[str] = None
    service_type: Optional[ServiceType] = None
    project_deadline: Optional[date] = None
    project_documents: Optional[List[DocumentInfo]] = None
    preferred_contact_number: Optional[str] = None
    client_name: Optional[str] = None
    # Deltek Integration Fields
    deltek_invoice_id: Optional[str] = None
    deltek_project_number: Optional[str] = None
    deltek_billing_client_id: Optional[str] = None
    deltek_original_amount: Optional[str] = None
    deltek_invoice_date: Optional[datetime] = None
    is_deltek_import: bool = False
    deltek_last_sync: Optional[datetime] = None

# Properties to receive via API on creation
class JobCreate(BaseModel):
    job_name: str
    job_description: Optional[str] = None
    job_status: Optional[JobStatus] = JobStatus.UNDER_REVIEW  # Default status
    contract_term: Optional[str] = None
    contract_amount: Optional[str] = None
    payment_id: Optional[str] = None
    service_type: Optional[ServiceType] = None
    project_deadline: Optional[date] = None
    # Note: project_documents are handled separately via file upload endpoints
    preferred_contact_number: Optional[str] = None
    client_name: Optional[str] = None

# Properties to receive via API on update
class JobUpdate(BaseModel):
    job_name: Optional[str] = None
    job_description: Optional[str] = None
    job_status: Optional[JobStatus] = None
    contract_term: Optional[str] = None
    contract_amount: Optional[str] = None
    payment_id: Optional[str] = None
    service_type: Optional[ServiceType] = None
    project_deadline: Optional[date] = None
    # Note: project_documents are handled separately via file upload endpoints
    preferred_contact_number: Optional[str] = None
    client_name: Optional[str] = None

# Properties to return via API
class Job(JobBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    billings: Optional[List[JobBilling]] = None

    class Config:
        from_attributes = True

# Job list response
class JobList(BaseModel):
    jobs: List[Job]
    total: int
    page: int
    size: int
