from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from app.core.config import settings
from app.api.v1.api import api_router
from app.core.scheduler import scheduler_service
import os
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events"""
    # Startup
    print("🚀 FastAPI lifespan startup called")
    try:
        logger.info("🚀 Starting application...")
        print("📅 Starting scheduler service...")
        scheduler_service.start()
        print("✅ Scheduler service started")
        logger.info("✅ Application startup completed")
        print("✅ Application startup completed")
    except Exception as e:
        error_msg = f"❌ Error during startup: {str(e)}"
        logger.error(error_msg)
        print(error_msg)
        import traceback
        traceback.print_exc()

    yield

    # Shutdown
    print("🛑 FastAPI lifespan shutdown called")
    try:
        logger.info("🛑 Shutting down application...")
        print("🛑 Stopping scheduler service...")
        scheduler_service.stop()
        print("✅ Scheduler service stopped")
        logger.info("✅ Application shutdown completed")
        print("✅ Application shutdown completed")
    except Exception as e:
        error_msg = f"❌ Error during shutdown: {str(e)}"
        logger.error(error_msg)
        print(error_msg)

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="FastAPI project with PostgreSQL, Authentication and Authorization",
    lifespan=lifespan
)

# Set all CORS enabled origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # or restrict to specific domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    return {"message": "Welcome to FastAPI with Authentication and Authorization"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/job-form")
async def job_form():
    """Serve the job creation form"""
    if os.path.exists("job_creation_form.html"):
        return FileResponse("job_creation_form.html")
    return {"message": "Job creation form not found"}
